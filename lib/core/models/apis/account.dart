import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/http_config.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';

class AccountApi {
  /// 获取账号金额相关信息
  static Future<AccountInfo?> fetchAccountInfo({MarketCategory? category}) async {
    final res = await Http().request<AccountInfo>(
      ApiEndpoints.getAccountInfo,
      method: HttpMethod.get,
      queryParameters: {
        if (category != null) "dataType": category.code,
      },
    );
    return res.data;
  }

  static Future<List<int>> fetchMarketOpenAsset() async {
    final res = await Http().request(
      ApiEndpoints.getMarketOpenAsset,
      method: HttpMethod.get,
    );
    final x = res.data['list'] ?? [];
    return x;
  }
}
